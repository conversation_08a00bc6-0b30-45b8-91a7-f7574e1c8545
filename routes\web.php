<?php


use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\PostController;
use App\Http\Controllers\LikeController;
use App\Http\Controllers\UserLevelController;
use App\Http\Controllers\UserController;
use App\Http\Livewire\UserProfileForm;
use Livewire\Volt\Volt;
use App\Http\Livewire\PostFeed;
use App\Livewire\CreatePost;
use App\Livewire\ProfileComponent;
use App\Http\Livewire\FollowRequestsHandler;
use App\Http\Livewire\ContosForm;
use App\Http\Controllers\GroupController;
use App\Http\Livewire\NearbyUsers;
use App\Models\Post;
use App\Livewire\CreateConto;
use App\Livewire\EditConto;
use App\Livewire\Timeline;
use App\Http\Controllers\PaymentController;
use App\Livewire\Messages;
use App\Livewire\UserPointsHistory;
use App\Models\Message;
use App\Http\Controllers\MessageController;
use App\Http\Controllers\LocationController;
use App\Livewire\Debug\FileUploadDebug;
use App\Http\Controllers\Shop\ShopController;
use App\Http\Controllers\Shop\OrderController;
use App\Http\Controllers\JobVacancyController;
use App\Http\Controllers\JobApplicationController;



Route::get('/', function () {
    return view('home');
})->name('home');

Route::get('/landing', function () {
    return view('landing');
})->name('landing');



Route::view('color-guide', 'color-guide')->name('color-guide');

// Rota de verificação de email (principal)
Route::get('/verify-email', function () {
    if (!Auth::check()) {
        return redirect()->route('login');
    }

    $user = Auth::user();

    // Se já está verificado, redireciona para dashboard
    if ($user->hasVerifiedEmail()) {
        return redirect()->route('dashboard')->with('success', 'Seu email já foi verificado!');
    }

    return view('auth.verify-email');
})->name('verification.notice');

// Rota alternativa para verificação de email
Route::get('/verificar-email', function () {
    if (!Auth::check()) {
        return redirect()->route('login');
    }

    $user = Auth::user();

    // Se já está verificado, redireciona para dashboard
    if ($user->hasVerifiedEmail()) {
        return redirect()->route('dashboard')->with('success', 'Seu email já foi verificado!');
    }

    return view('auth.verify-email');
})->name('verification.alternative');

// Rota para reenviar email de verificação
Route::post('/email/verification-notification', function () {
    if (!Auth::check()) {
        return redirect()->route('login');
    }

    $user = Auth::user();

    if ($user->hasVerifiedEmail()) {
        return redirect()->route('dashboard');
    }

    $user->sendEmailVerificationNotification();

    return back()->with('status', 'verification-link-sent');
})->middleware('throttle:6,1')->name('verification.send');



Route::view('dashboard', 'dashboard')
    ->middleware(['auth', 'verified.redirect', 'visitor.time'])
    ->name('dashboard');

Route::view('busca', 'busca')
    ->middleware(['auth', 'verified.redirect', 'visitor.time'])
    ->name('busca');

// Rota temporária para teste
Route::get('busca-test', function () {
    return view('busca-test');
})->middleware(['auth', 'verified.redirect', 'visitor.time'])->name('busca.test');


Route::get('/contos', function () {
    return view('contos');
})->middleware(['auth', 'verified.redirect', 'visitor.time'])->name('contos');

Route::get('/contos/{id}', function ($id) {
    $conto = App\Models\Conto::with(['user', 'category'])->find($id);
    return view('livewire.show-conto', compact('conto'));
})->middleware(['auth', 'verified.redirect', 'visitor.time'])->name('contos.show');

// Live Streams Routes
Route::middleware(['auth', 'verified.redirect', 'visitor.time'])->group(function () {

    Route::get('/lives', function () {
        return view('live-streams.index');
    })->name('live-streams.index');

    Route::get('/lives/replays', function () {
        return view('live-streams.replays');
    })->name('live-streams.replays');

    Route::get('/lives/replays-test', function () {
        $replays = App\Models\LiveStream::where('has_replay', true)->with('user')->get();
        return response()->json([
            'count' => $replays->count(),
            'replays' => $replays->map(function ($replay) {
                return [
                    'id' => $replay->id,
                    'title' => $replay->title,
                    'user' => $replay->user->name,
                    'has_replay' => $replay->has_replay,
                    'video_path' => $replay->video_path
                ];
            })
        ]);
    })->name('live-streams.replays.test');

    Route::get('/lives/transmitir', function () {
        return view('live-streams.broadcast');
    })->name('live-streams.broadcast');

    Route::get('/lives/{liveStream}', function (App\Models\LiveStream $liveStream) {
        return view('live-streams.view', compact('liveStream'));
    })->name('live-streams.view');

    // API Routes para Streaming
    Route::post('/api/live-streams/start', [App\Http\Controllers\LiveStreamController::class, 'startStream']);
    Route::post('/api/live-streams/end', [App\Http\Controllers\LiveStreamController::class, 'endStream']);
    Route::post('/api/live-streams/update-viewers', [App\Http\Controllers\LiveStreamController::class, 'updateViewers']);
    Route::post('/api/live-streams/save-recording', [App\Http\Controllers\LiveStreamController::class, 'saveRecording']);

    // Rota para teste de upload (apenas para administradores)
    Route::get('/admin/upload-test', function () {
        return view('admin.upload-test');
    })->middleware('admin')->name('admin.upload-test');
});


// Feeds Fullscreen (principais)
Route::get('feed_imagens', App\Livewire\ImageFeed::class)
    ->middleware(['auth', 'verified.redirect', 'visitor.time'])
    ->name('feed_imagens');

Route::get('feed_videos', App\Livewire\VideoFeed::class)
    ->middleware(['auth', 'verified.redirect', 'visitor.time'])
    ->name('feed_videos');

// Feeds Grid (visualização em grade)
Route::get('feed_imagens_grid', function () {
    $posts = Post::whereNotNull('image')
        ->whereNull('group_id') // Exclui postagens de grupos do feed principal
        ->with(['user'])
        ->latest()
        ->get();
    return view('feed_imagens', compact('posts'));
})->middleware(['auth', 'verified.redirect', 'visitor.time'])->name('feed_imagens_grid');

Route::get('feed_videos_grid', function () {
    $posts = Post::whereNotNull('video')
        ->whereNull('group_id') // Exclui postagens de grupos do feed principal
        ->with(['user', 'user.userPhotos'])
        ->latest()
        ->get();
    return view('feed_videos_grid', compact('posts'));
})->middleware(['auth', 'verified.redirect', 'visitor.time'])->name('feed_videos_grid');


// Rotas de eventos
Route::middleware(['auth', 'verified.redirect', 'visitor.time'])->group(function () {
    Route::get('/eventos', [App\Http\Controllers\EventController::class, 'index'])->name('events.index');
    Route::get('/eventos/{slug}', [App\Http\Controllers\EventController::class, 'show'])->name('events.show');
    Route::post('/eventos/{event}/registrar', [App\Http\Controllers\EventAttendeeController::class, 'register'])
        ->name('events.register');
    Route::get('/eventos/{event}/pagamento/sucesso/{attendee}', [App\Http\Controllers\EventAttendeeController::class, 'paymentSuccess'])->name('events.payment.success');
    Route::get('/eventos/{event}/pagamento/cancelar/{attendee}', [App\Http\Controllers\EventAttendeeController::class, 'paymentCancel'])->name('events.payment.cancel');
    Route::post('/eventos/{event}/cancelar', [App\Http\Controllers\EventAttendeeController::class, 'cancel'])->name('events.cancel');
});

// Rotas de administração de eventos
Route::middleware(['auth', 'admin'])->group(function () {
    Route::get('/eventos/criar', [App\Http\Controllers\EventController::class, 'create'])->name('events.create');
    Route::post('/eventos', [App\Http\Controllers\EventController::class, 'store'])->name('events.store');
    Route::get('/eventos/{event}/editar', [App\Http\Controllers\EventController::class, 'edit'])->name('events.edit');
    Route::put('/eventos/{event}', [App\Http\Controllers\EventController::class, 'update'])->name('events.update');
    Route::delete('/eventos/{event}', [App\Http\Controllers\EventController::class, 'destroy'])->name('events.destroy');
    Route::post('/eventos/{event}/participantes/{attendee}/check-in', [App\Http\Controllers\EventAttendeeController::class, 'checkIn'])->name('events.attendee.check-in');
});


// Radar routes
Route::view('/radar', 'radar')->name('radar')->middleware(['auth', 'visitor.time']);
Route::post('/update-user-location', [LocationController::class, 'updateLocation'])->middleware(['auth', 'visitor.time']);

// Rotas de grupos
Route::prefix('grupos')->name('grupos.')->middleware(['auth', 'verified', 'visitor.time'])->group(function () {
    Route::get('/', [App\Http\Controllers\GroupController::class, 'index'])->name('index');
    Route::get('/criar', function () {
        return view('groups.create');
    })->name('create');
    Route::post('/', [App\Http\Controllers\GroupController::class, 'store'])->name('store');
    Route::get('/{group:slug}', [App\Http\Controllers\GroupController::class, 'show'])->name('show');
    Route::get('/{group:slug}/editar', [App\Http\Controllers\GroupController::class, 'edit'])->name('edit');
    Route::put('/{group}', [App\Http\Controllers\GroupController::class, 'update'])->name('update');
    Route::delete('/{group}', [App\Http\Controllers\GroupController::class, 'destroy'])->name('destroy');

    // Rotas de membros
    Route::get('/{group:slug}/membros', [App\Http\Controllers\GroupController::class, 'members'])->name('members');
    Route::get('/minhas-inscricoes', [App\Http\Controllers\GroupController::class, 'myMemberships'])->name('my-memberships');
    Route::post('/{group}/membros/{user}/aprovar', [App\Http\Controllers\GroupMemberController::class, 'approve'])->name('members.approve');
    Route::post('/{group}/membros/{user}/rejeitar', [App\Http\Controllers\GroupMemberController::class, 'reject'])->name('members.reject');
    Route::post('/{group}/membros/{user}/remover', [App\Http\Controllers\GroupMemberController::class, 'remove'])->name('members.remove');
    Route::post('/{group}/membros/{user}/alterar-funcao', [App\Http\Controllers\GroupMemberController::class, 'changeRole'])->name('members.change-role');

    // Rotas de convites
    Route::get('/convites', [App\Http\Controllers\GroupInvitationController::class, 'index'])->name('invitations.index');
    Route::post('/{group}/convidar', [App\Http\Controllers\GroupInvitationController::class, 'store'])->name('invitations.store');
    Route::put('/convites/{invitation}/aceitar', [App\Http\Controllers\GroupInvitationController::class, 'accept'])->name('invitations.accept');
    Route::put('/convites/{invitation}/recusar', [App\Http\Controllers\GroupInvitationController::class, 'decline'])->name('invitations.decline');
    Route::delete('/convites/{invitation}/cancelar', [App\Http\Controllers\GroupInvitationController::class, 'cancel'])->name('invitations.cancel');

    // Rotas de ações
    Route::post('/{group}/entrar', [App\Http\Controllers\GroupController::class, 'join'])->name('join');
    Route::post('/{group}/sair', [App\Http\Controllers\GroupController::class, 'leave'])->name('leave');
});



Route::get('caixa_de_mensagens', function () {
    return view('caixa_de_mensagens');
})
    ->middleware(['auth', 'verified.redirect', 'visitor.time'])
    ->name('caixa_de_mensagens');

Route::get('sugestoes', function () {
    return view('sugestoes');
})
    ->middleware(['auth', 'verified.redirect', 'visitor.time'])
    ->name('sugestoes');

Route::view('mindmap', 'mindmap')
    ->middleware(['auth', 'verified.redirect'])
    ->name('mindmap');

Route::view('pesquisas', 'pesquisas')
    ->middleware(['auth', 'verified.redirect'])
    ->name('pesquisas');


Route::view('renovar-vip', 'renovar-vip')
    ->middleware(['auth', 'verified.redirect'])
    ->name('renovar-vip');

// Rotas de assinatura VIP
Route::middleware(['auth', 'verified.redirect'])->group(function () {
    Route::post('/vip/checkout', [App\Http\Controllers\VipSubscriptionController::class, 'createCheckoutSession'])->name('vip.checkout');
    Route::get('/vip/pagamento/sucesso', [App\Http\Controllers\VipSubscriptionController::class, 'paymentSuccess'])->name('vip.payment.success');
    Route::get('/vip/pagamento/cancelar', [App\Http\Controllers\VipSubscriptionController::class, 'paymentCancel'])->name('vip.payment.cancel');
    Route::post('/vip/cancelar', [App\Http\Controllers\VipSubscriptionController::class, 'cancelSubscription'])->name('vip.cancel');
    Route::post('/vip/reativar', [App\Http\Controllers\VipSubscriptionController::class, 'resumeSubscription'])->name('vip.resume');
});

// Rota de webhook do Stripe
Route::post('/stripe/webhook', [App\Http\Controllers\StripeWebhookController::class, 'handleWebhook']);


Route::view('loja-virtual', 'loja-virtual')
    ->name('loja.virtual');


// Rotas da loja
Route::get('/loja', App\Livewire\Shop\ProductList::class)->name('shop.index');
Route::get('/loja/categoria/{slug}', App\Livewire\Shop\ProductList::class)->name('shop.category');
Route::get('/loja/produto/{slug}', App\Livewire\Shop\ProductDetail::class)->name('shop.product');

// Rotas da loja que requerem autenticação
Route::middleware(['auth'])->group(function () {
    Route::get('/loja/carrinho', App\Livewire\Shop\ShoppingCart::class)->name('shop.cart');
    Route::get('/loja/checkout', App\Livewire\Shop\Checkout::class)->name('shop.checkout');
    Route::get('/loja/pedido/{id}/sucesso', App\Livewire\Shop\OrderSuccess::class)->name('shop.order.success');
    Route::get('/loja/meus-pedidos', App\Livewire\Shop\UserOrders::class)->name('shop.user.orders');
    Route::get('/loja/pedido/{id}', App\Livewire\Shop\OrderDetail::class)->name('shop.order.detail');
    Route::get('/loja/lista-desejos', App\Livewire\Shop\Wishlist::class)->name('shop.wishlist');

    // Rotas para produtos digitais
    Route::get('/loja/meus-downloads', [App\Http\Controllers\DigitalProductController::class, 'index'])->name('shop.downloads');
    Route::get('/loja/download/{id}', [App\Http\Controllers\DigitalProductController::class, 'download'])->name('shop.downloads.download');
});

// Rotas da carteira
Route::middleware(['auth'])->prefix('carteira')->group(function () {
    Route::get('/', [App\Http\Controllers\WalletController::class, 'index'])->name('wallet.index');

    // Adicionar fundos
    Route::get('/adicionar', function () {
        return view('wallet.add-funds');
    })->name('wallet.add-funds');
    Route::post('/adicionar', [App\Http\Controllers\WalletController::class, 'processAddFunds'])->name('wallet.add-funds.process');
    Route::get('/adicionar/sucesso', [App\Http\Controllers\WalletController::class, 'addFundsSuccess'])->name('wallet.add-funds.success');
    Route::get('/adicionar/cancelar', [App\Http\Controllers\WalletController::class, 'addFundsCancel'])->name('wallet.add-funds.cancel');

    // Transferir fundos
    Route::get('/transferir', function () {
        return view('wallet.transfer');
    })->name('wallet.transfer');
    Route::post('/transferir', [App\Http\Controllers\WalletController::class, 'processTransferFunds'])->name('wallet.transfer.process');

    // Sacar fundos
    Route::get('/sacar', function () {
        return view('wallet.withdraw');
    })->name('wallet.withdraw');
    Route::post('/sacar', [App\Http\Controllers\WalletController::class, 'processWithdrawFunds'])->name('wallet.withdraw.process');
});



// Rota de teste de pagamentos (apenas para administradores)
Route::middleware(['auth', 'admin'])->group(function () {
    Route::get('/test-payments', App\Livewire\PaymentTest::class)->name('test.payments');
    Route::get('/test-vip-checkout', App\Livewire\VipTestCheckout::class)->name('test.vip.checkout');
});

// Rotas do Monte Sua Noite
Route::middleware(['auth'])->prefix('monte-sua-noite')->group(function () {
    Route::get('/', App\Livewire\NightBoard\PlanList::class)->name('night-board.index');
    Route::get('/criar', App\Livewire\NightBoard\Builder::class)->name('night-board.create');
    Route::get('/editar/{plan}', App\Livewire\NightBoard\Builder::class)->name('night-board.edit');
    Route::get('/{plan}', App\Livewire\NightBoard\ShowPlan::class)->name('night-board.show');
});
// Rotas de Suporte e Ajuda
Route::middleware(['auth', 'verified.redirect', 'visitor.time'])->prefix('suporte')->group(function () {
    Route::get('/', App\Livewire\Support\HelpCenter::class)->name('support.index');
    Route::get('/tickets/{ticket}', App\Livewire\Support\TicketView::class)->name('support.tickets.show');
    Route::get('/artigos/{article}', function (\App\Models\HelpArticle $article) {
        $article->incrementViewCount();
        return view('support.article-view', compact('article'));
    })->name('support.articles.show');
});

// Rotas de administração
Route::middleware(['auth', 'admin'])->prefix('admin')->group(function () {
    Route::get('/', App\Livewire\Admin\Dashboard::class)->name('admin.dashboard');
    Route::get('/suporte', App\Livewire\Admin\SupportTickets::class)->name('admin.support.tickets');
    Route::get('/produtos', App\Livewire\Admin\ProductManager::class)->name('admin.products');
    Route::get('/categorias', App\Livewire\Admin\CategoryManager::class)->name('admin.categories'); // categorias da loja
    Route::get('/pedidos', App\Livewire\Admin\OrderManager::class)->name('admin.orders');
    Route::get('/cupons', App\Livewire\Admin\CouponManager::class)->name('admin.coupons');
Route::get('/usuarios', App\Livewire\Admin\UserManager::class)->name('admin.users');
    Route::get('/carteiras', App\Livewire\Admin\WalletManager::class)->name('admin.wallets');
    Route::get('/configuracoes', App\Livewire\Admin\Settings::class)->name('admin.settings');
    Route::get('/eventos', App\Livewire\Admin\EventManager::class)->name('admin.events');
});

// Rotas administrativas
Route::prefix('admin')->name('admin.')->middleware(['auth', 'admin'])->group(function () {
    // Rota para gerenciar níveis
    Route::get('/levels', App\Livewire\Admin\ManageLevels::class)->name('levels');
    // Rota para gerenciar conquistas
    Route::get('/achievements', App\Livewire\Admin\ManageAchievements::class)->name('achievements');
});

Route::get('/meus-pagamentos', [PaymentController::class, 'index'])->name('meus-pagamentos');

// Rotas para pagamento de drink
Route::middleware(['auth'])->group(function () {
    Route::get('/payment/success/{userId}', [PaymentController::class, 'paymentSuccess'])->name('payment.success');
    Route::get('/payment/cancel', [PaymentController::class, 'paymentCancel'])->name('payment.cancel');
});




Route::middleware(['auth', 'visitor.time'])->group(function () {
    Route::redirect('settings', 'settings/profile');
    Volt::route('settings/profile', 'settings.profile')->name('settings.profile');
    Volt::route('settings/password', 'settings.password')->name('settings.password');
    Volt::route('settings/appearance', 'settings.appearance')->name('settings.appearance');
    Volt::route('settings/preferences', 'settings.preferences-form')->name('settings.preferences');
    Volt::route('settings/profile-with-avatar', 'settings.profile-with-avatar')->name('settings.profile-with-avatar');
    Volt::route('settings/profile-with-cover', 'settings.profile-with-cover')->name('settings.profile-with-cover');
    Route::get('/follow-requests', function () {
        return view('follow-requests');
    })->name('follow.requests');
    Route::get('/contos/create', CreateConto::class)->name('contos.create');
    Route::get('/contos/{conto}/edit', EditConto::class)->name('contos.edit')->where('conto', '[0-9]+');
    Route::delete('/contos/{conto}', function ($conto) {
        $conto = App\Models\Conto::findOrFail($conto);

        if (Auth::id() !== $conto->user_id) {
            abort(403, 'Você não tem permissão para excluir este conto.');
        }

        $conto->delete();

        return redirect()->route('contos')->with('message', 'Conto excluído com sucesso!');
    })->name('contos.destroy');
    Route::get('/timeline', Timeline::class)->name('timeline');
    Route::resource('messages', MessageController::class)->only(['index', 'store', 'destroy']);

    // Rotas para o histórico de pontos
    Route::get('/pontos', function () {
        return view('points-history');
    })->name('points.history');

    Route::get('/pontos/{userId}', function ($userId) {
        return view('points-history', ['userId' => $userId]);
    })->name('points.history.user');

    // Rota para visualizar visitantes do perfil
    Route::get('/meus-visitantes', function () {
        return view('profile-visitors');
    })->name('profile.visitors');

    // Rota para ranking de usuários
    Route::get('/ranking', function () {
        return view('ranking');
    })->name('ranking');
});


// Rota para alternar curtidas (Livewire pode ser usado, mas aqui um POST simples)
Route::post('likes/toggle/{post}', [LikeController::class, 'toggle'])->name('likes.toggle')->middleware('auth');



// Rota para processar o upload da foto
Route::post('/user/upload-photo', [UserController::class, 'uploadPhoto'])->name('user.uploadPhoto');

// Incluir rotas de autenticação ANTES das rotas catch-all
require __DIR__ . '/auth.php';


// Rotas para hashtags
Route::prefix('hashtag')->name('hashtag.')->group(function () {
    Route::get('/', [App\Http\Controllers\HashtagController::class, 'index'])->name('index');
    Route::get('/search', [App\Http\Controllers\HashtagController::class, 'search'])->name('search');
    Route::get('/{hashtag:slug}', [App\Http\Controllers\HashtagController::class, 'show'])->name('show');
});



Route::get('post/{post}', function (Post $post) {
    $post->load(['user.userPhotos', 'likes', 'comments']);
    return view('post.show', compact('post'));
})->name('post.show');

// Shop routes
Route::prefix('loja')->name('shop.')->middleware(['auth', 'verified.redirect', 'visitor.time'])->group(function () {
    Route::get('/', [ShopController::class, 'index'])->name('index');
    Route::get('/produto/{product:slug}', [ShopController::class, 'show'])->name('product.show');
    Route::get('/carrinho', \App\Livewire\Shop\ShoppingCart::class)->name('cart');
    Route::get('/checkout', App\Livewire\Shop\Checkout::class)->name('checkout');
    Route::get('/meus-downloads', [ShopController::class, 'downloads'])->name('downloads');
    Route::get('/download/{productDownload}', [ShopController::class, 'downloadFile'])->name('download.file');

    // Order and Checkout Callbacks
    Route::get('/checkout/success/{order}', [OrderController::class, 'paymentSuccess'])->name('checkout.success');
    Route::get('/checkout/cancel/{order}', [OrderController::class, 'paymentCancel'])->name('checkout.cancel');
    Route::get('/pedido/{order}', [OrderController::class, 'show'])->name('order.show');
    Route::get('/pedido/sucesso/{order}', [OrderController::class, 'showSuccess'])->name('order.success');
});

// ========================================
// SISTEMA DE VAGAS DE EMPREGO ("TRABALHE CONOSCO")
// ========================================

// Rotas públicas de vagas (sem autenticação)
Route::prefix('trabalhe-conosco')->name('job_vacancies.')->group(function () {
    Route::get('/', [App\Http\Controllers\JobVacancyController::class, 'index'])->name('index');
    Route::get('/vaga/{slug}', [App\Http\Controllers\JobVacancyController::class, 'show'])->name('show');
});

// Rotas de candidatura (usuário autenticado)
Route::middleware(['auth'])->prefix('trabalhe-conosco')->name('job_vacancies.')->group(function () {
    // Candidatar-se a uma vaga
    Route::post('/vaga/{job_vacancy}/candidatar', [App\Http\Controllers\JobApplicationController::class, 'store'])->name('apply');

    // Gerenciar candidaturas do usuário
    Route::get('/minhas-candidaturas', [App\Http\Controllers\JobApplicationController::class, 'myApplications'])->name('my-applications');
    Route::get('/candidatura/{application}', [App\Http\Controllers\JobApplicationController::class, 'show'])->name('application.show');
    Route::delete('/candidatura/{application}', [App\Http\Controllers\JobApplicationController::class, 'destroy'])->name('application.cancel');
    Route::get('/candidatura/{application}/curriculo', [App\Http\Controllers\JobApplicationController::class, 'downloadResume'])->name('application.resume');
});

// Rotas administrativas (apenas admin)
Route::middleware(['auth', 'admin'])->prefix('admin')->name('admin.')->group(function () {
    // Gerenciamento de vagas
    Route::get('/vagas', App\Livewire\Admin\JobVacancyManager::class)->name('job_vacancies');

    // Gerenciamento de candidaturas
    Route::get('/candidaturas', App\Livewire\Admin\JobApplicationManager::class)->name('job_applications');

    // Gerenciamento de categorias de vagas
    Route::get('/categorias-vagas', App\Livewire\Admin\JobCategoryManager::class)->name('job_categories');
});
// Rota para exibir perfil do usuário pelo username
Route::get('/{username}', function ($username) {
    return view('profile-page', ['username' => $username]);
})->name('user.profile');
